 :root {
     --primary: #4361ee;
     --primary-dark: #3a56d4;
     --secondary: #7209b7;
     --dark: #1e1e2d;
     --light: #f8f9fa;
     --gray: #6c757d;
     --light-gray: #e9ecef;
     --success: #06d6a0;
     --ai-bubble: #f0f4ff;
     --user-bubble: #e6f7ff;
     --shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
     --border-radius: 12px;
     --transition: all 0.3s ease;
 }

 * {
     margin: 0;
     padding: 0;
     box-sizing: border-box;
     font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
 }

 body {
     background: linear-gradient(135deg, #f5f7ff 0%, #f0f2f8 100%);
     min-height: 100vh;
     color: var(--dark);
     line-height: 1.6;
 }

 /* Header Styles */
 header {
     background: linear-gradient(to right, var(--primary), var(--secondary));
     color: white;
     padding: 1rem 2rem;
     box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
     position: sticky;
     top: 0;
     z-index: 100;
     display: flex;
     justify-content: space-between;
     align-items: center;
 }

 .logo {
     font-size: 1.8rem;
     font-weight: 700;
     display: flex;
     align-items: center;
 }

 .logo::before {
     content: "📊";
     margin-right: 10px;
     font-size: 1.5rem;
 }

 nav ul {
     display: flex;
     list-style: none;
     gap: 2rem;
 }

 nav a {
     color: rgba(255, 255, 255, 0.85);
     text-decoration: none;
     font-weight: 500;
     padding: 0.5rem 0;
     position: relative;
     transition: var(--transition);
 }

 nav a:hover,
 nav a.active {
     color: white;
 }

 nav a::after {
     content: '';
     position: absolute;
     bottom: 0;
     left: 0;
     width: 0;
     height: 2px;
     background-color: white;
     transition: var(--transition);
 }

 nav a:hover::after,
 nav a.active::after {
     width: 100%;
 }

 .github-icon a {
     color: white;
     font-size: 1.5rem;
     transition: var(--transition);
 }

 .github-icon a:hover {
     transform: translateY(-3px);
     color: var(--light-gray);
 }

 /* Main Chat Container */
 .chat-layout {
     display: flex;
     justify-content: center;
     padding: 2rem 1rem;
     max-width: 1200px;
     margin: 0 auto;
 }

 .chat-container {
     width: 100%;
     max-width: 900px;
     background: white;
     border-radius: var(--border-radius);
     box-shadow: var(--shadow);
     overflow: hidden;
     display: flex;
     flex-direction: column;
     height: calc(100vh - 120px);
 }

 /* Chat Header */
 .chat-header {
     display: flex;
     justify-content: space-between;
     align-items: center;
     padding: 1.2rem 1.5rem;
     background: white;
     border-bottom: 1px solid var(--light-gray);
     box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
 }

 .chat-title {
     display: flex;
     align-items: center;
     gap: 1rem;
 }

 .ai-avatar {
     width: 42px;
     height: 42px;
     background: linear-gradient(135deg, var(--primary), var(--secondary));
     border-radius: 50%;
     display: flex;
     align-items: center;
     justify-content: center;
     color: white;
     font-size: 1.2rem;
 }

 .chat-info h3 {
     font-size: 1.3rem;
     font-weight: 600;
     color: var(--dark);
 }

 .status {
     display: flex;
     align-items: center;
     gap: 0.5rem;
     font-size: 0.9rem;
     color: var(--success);
     font-weight: 500;
 }

 .status::before {
     content: "";
     width: 8px;
     height: 8px;
     background: var(--success);
     border-radius: 50%;
     display: inline-block;
 }

 .chat-actions {
     display: flex;
     gap: 0.8rem;
 }

 .action-btn {
     width: 38px;
     height: 38px;
     border-radius: 50%;
     background: var(--light-gray);
     border: none;
     color: var(--gray);
     cursor: pointer;
     transition: var(--transition);
     display: flex;
     align-items: center;
     justify-content: center;
 }

 .action-btn:hover {
     background: var(--primary);
     color: white;
     transform: translateY(-2px);
 }

 /* Disclaimer */
 .disclaimer {
     background: #eef7ff;
     padding: 0.8rem 1.5rem;
     font-size: 0.9rem;
     display: flex;
     align-items: center;
     gap: 0.8rem;
     color: #0d6efd;
     border-bottom: 1px solid #d0e6ff;
 }

 .disclaimer i {
     font-size: 1.2rem;
 }

 /* Chat Messages */
 .chat-messages {
     flex: 1;
     overflow-y: auto;
     padding: 1.5rem;
     display: flex;
     flex-direction: column;
     gap: 1.5rem;
     background: #fafbff;
     background-image:
         radial-gradient(#e0e7ff 1px, transparent 1px),
         radial-gradient(#e0e7ff 1px, transparent 1px);
     background-size: 40px 40px;
     background-position: 0 0, 20px 20px;
 }

 .message {
     display: flex;
     gap: 1rem;
     max-width: 85%;
     animation: fadeIn 0.3s ease;
 }

 @keyframes fadeIn {
     from {
         opacity: 0;
         transform: translateY(10px);
     }

     to {
         opacity: 1;
         transform: translateY(0);
     }
 }

 .ai-message {
     align-self: flex-start;
 }

 .user-message {
     align-self: flex-end;
     flex-direction: row-reverse;
 }

 .message-content {
     padding: 1rem 1.2rem;
     border-radius: 18px;
     font-size: 1rem;
     line-height: 1.5;
     position: relative;
     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
 }

 .ai-message .message-content {
     background: var(--ai-bubble);
     border-bottom-left-radius: 4px;
     color: var(--dark);
 }

 .user-message .message-content {
     background: var(--user-bubble);
     border-bottom-right-radius: 4px;
     color: var(--dark);
 }

 .welcome-message .message-content {
     background: linear-gradient(135deg, #f0f4ff, #e6eeff);
     border: 1px solid #d0ddff;
     padding: 1.5rem;
 }

 .message-content p {
     margin-bottom: 0.5rem;
 }

 .message-content p:last-child {
     margin-bottom: 0;
 }

 .message-content ul {
     padding-left: 1.5rem;
     margin: 0.8rem 0;
 }

 .message-content li {
     margin-bottom: 0.5rem;
 }

 .message-content li::marker {
     color: var(--primary);
 }

 /* Input Area */
 .input-area {
     padding: 1.5rem;
     background: white;
     border-top: 1px solid var(--light-gray);
 }

 .input-container {
     position: relative;
 }

 .input-wrapper {
     display: flex;
     border: 1px solid var(--light-gray);
     border-radius: 30px;
     overflow: hidden;
     background: white;
     box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
     transition: var(--transition);
 }

 .input-wrapper:focus-within {
     border-color: var(--primary);
     box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
 }

 #user-input {
     flex: 1;
     border: none;
     padding: 1rem 1.5rem;
     font-size: 1rem;
     outline: none;
     background: transparent;
 }

 #user-input::placeholder {
     color: var(--gray);
     opacity: 0.7;
 }

 #send-btn {
     width: 50px;
     background: var(--primary);
     color: white;
     border: none;
     cursor: pointer;
     transition: var(--transition);
     display: flex;
     align-items: center;
     justify-content: center;
 }

 #send-btn:not(:disabled):hover {
     background: var(--primary-dark);
 }

 #send-btn:disabled {
     background: var(--light-gray);
     color: var(--gray);
     cursor: not-allowed;
 }

 .input-footer {
     display: flex;
     justify-content: center;
     margin-top: 0.8rem;
     font-size: 0.85rem;
     color: var(--gray);
 }

 .powered-by {
     display: flex;
     align-items: center;
     gap: 0.5rem;
 }

 /* Responsive Design */
 @media (max-width: 768px) {
     header {
         flex-direction: column;
         padding: 1rem;
         gap: 1rem;
     }

     nav ul {
         gap: 1.2rem;
     }

     .chat-container {
         height: calc(100vh - 160px);
     }

     .message {
         max-width: 90%;
     }

     .chat-header {
         padding: 1rem;
     }

     .chat-info h3 {
         font-size: 1.1rem;
     }

     .disclaimer {
         padding: 0.7rem 1rem;
         font-size: 0.8rem;
     }

     .chat-messages {
         padding: 1rem;
     }

     .input-area {
         padding: 1rem;
     }
 }

 @media (max-width: 480px) {
     nav ul {
         gap: 0.8rem;
         flex-wrap: wrap;
         justify-content: center;
     }

     .message {
         max-width: 95%;
         gap: 0.7rem;
     }

     .ai-avatar {
         width: 36px;
         height: 36px;
         font-size: 1rem;
     }

     .message-content {
         padding: 0.8rem;
         font-size: 0.95rem;
     }

     .welcome-message .message-content {
         padding: 1rem;
     }

     #user-input {
         padding: 0.8rem 1rem;
     }
 }

 /* Typing Indicator */
 .typing-indicator {
     display: flex;
     align-items: center;
     gap: 1rem;
     max-width: 85%;
     align-self: flex-start;
 }

 .typing-bubble {
     padding: 12px 16px;
     background: var(--ai-bubble);
     border-radius: 18px;
     border-bottom-left-radius: 4px;
     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
     display: flex;
     align-items: center;
     gap: 8px;
 }

 .typing-text {
     color: var(--dark);
     font-size: 0.9rem;
 }

 .typing-dots {
     display: flex;
     gap: 4px;
 }

 .typing-dot {
     width: 8px;
     height: 8px;
     background: var(--primary);
     border-radius: 50%;
     animation: bounce 1.4s infinite ease-in-out;
 }

 .typing-dot:nth-child(1) {
     animation-delay: -0.32s;
 }

 .typing-dot:nth-child(2) {
     animation-delay: -0.16s;
 }

 @keyframes bounce {

     0%,
     80%,
     100% {
         transform: translateY(0);
     }

     40% {
         transform: translateY(-6px);
     }
 }